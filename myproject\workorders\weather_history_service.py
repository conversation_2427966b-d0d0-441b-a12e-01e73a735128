"""
Weather history service for fetching historical weather data from Meteomatics API.
Integrates with BigQuery for workorder coordinates and timeframes.
"""

import requests
import pytz
import time
from datetime import datetime
from typing import Dict, Tuple
from django.conf import settings
from django.core.cache import cache
from google.cloud import bigquery
from myproject.utils import get_bigquery_client
from .workorder_data import get_workorder_date_range
from .constants import PROJECT_FIELD_ACTIVITY_TABLE

from timezonefinder import TimezoneFinder


class WeatherHistoryServiceError(Exception):
    """Custom exception for weather history service errors"""
    pass


class WeatherHistoryService:
    """Service class for fetching historical weather data for workorders"""

    # Class constants
    RIGA_TIMEZONE = pytz.timezone('Europe/Riga')
    PARAMETER_MAPPING = {
        't_2m:C': 'temperature',
        'wind_speed_80m:ms': 'windSpeed',
        'wind_gusts_80m_1h:ms': 'windGusts',
        'precip_1h:mm': 'precipitation'
    }

    def __init__(self):
        self.username = settings.METEOMATICS_USERNAME
        self.password = settings.METEOMATICS_PASSWORD
        self.base_url = settings.METEOMATICS_BASE_URL
        self.timeout = 10  # seconds
        self.max_retries = 3
        self.cache_ttl = 3600  # 1 hour (historical data changes less frequently)
        self._timezone_finder = TimezoneFinder()  # Cache the instance

        if not self.username or not self.password:
            raise WeatherHistoryServiceError("METEOMATICS_USERNAME and METEOMATICS_PASSWORD not configured")
    
    def get_workorder_coordinates(self, workorder_id: int) -> Tuple[float, float]:
        """
        Get the latest coordinates for a workorder's set from BigQuery project_field_activity table.

        Args:
            workorder_id: ID of the workorder

        Returns:
            Tuple of (latitude, longitude)

        Raises:
            WeatherHistoryServiceError: If coordinates cannot be found
        """
        try:
            client = get_bigquery_client()

            # Query to get the latest activity coordinates for the workorder's set
            query = f"""
            SELECT turbine_latitude, turbine_longitude
            FROM {PROJECT_FIELD_ACTIVITY_TABLE}
            WHERE workorder_id = @workorder_id
              AND turbine_latitude IS NOT NULL
              AND turbine_longitude IS NOT NULL
            ORDER BY end_datetime DESC, start_datetime DESC
            LIMIT 1
            """

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("workorder_id", "INTEGER", workorder_id)
                ]
            )

            query_job = client.query(query, job_config=job_config)

            # Handle both regular BigQuery results and cached results
            try:
                results = query_job.result()
                results_list = list(results)
            except Exception as e:
                raise WeatherHistoryServiceError(f"Failed to execute query for workorder {workorder_id}: {str(e)}")

            if not results_list:
                raise WeatherHistoryServiceError(f"No coordinates found for workorder: {workorder_id}")

            row = results_list[0]
            latitude = float(row.turbine_latitude)
            longitude = float(row.turbine_longitude)

            return latitude, longitude

        except WeatherHistoryServiceError:
            # Re-raise WeatherHistoryServiceError without wrapping
            raise
        except Exception as e:
            raise WeatherHistoryServiceError(f"Failed to get coordinates for workorder {workorder_id}: {str(e)}")
    
    def _get_cache_key(self, workorder_id: int) -> str:
        """Generate cache key for workorder weather history data"""
        return f"weather_history_workorder:{workorder_id}"
    
    def _make_request(self, url: str) -> Dict:
        """
        Make HTTP request to Meteomatics API with retry logic.

        Args:
            url: Full API URL

        Returns:
            JSON response data
        """
        for attempt in range(self.max_retries):
            try:
                response = requests.get(
                    url,
                    auth=(self.username, self.password),
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 401:
                    raise WeatherHistoryServiceError("Authentication failed - check Meteomatics credentials")
                elif response.status_code == 429:
                    # Rate limited - wait and retry
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise WeatherHistoryServiceError(f"API request failed with status {response.status_code}: {response.text}")
                    
            except requests.exceptions.Timeout:
                if attempt == self.max_retries - 1:
                    raise WeatherHistoryServiceError("Request timeout")
                time.sleep(1)
            except requests.exceptions.RequestException as e:
                if attempt == self.max_retries - 1:
                    raise WeatherHistoryServiceError(f"Request failed: {str(e)}")
                time.sleep(1)

        raise WeatherHistoryServiceError("Maximum retry attempts exceeded")
    
    def get_workorder_weather_history(self, workorder_id: int) -> Dict:
        """
        Get historical weather data for a workorder's timeframe and location.

        Args:
            workorder_id: ID of the workorder

        Returns:
            Dict with normalized weather history data including dual timezone
        """
        # Check cache first
        cache_key = self._get_cache_key(workorder_id)
        cached_data = cache.get(cache_key)
        if cached_data:
            return cached_data

        # Get workorder coordinates and date range
        lat, lon = self.get_workorder_coordinates(workorder_id)
        start_date, end_date = get_workorder_date_range(workorder_id)

        if not start_date or not end_date:
            raise WeatherHistoryServiceError(f"No date range found for workorder: {workorder_id}")

        # Convert local database times to UTC for API request
        # Database times are in local timezone, but API expects UTC
        location_tz = self._get_timezone_for_location(lat, lon)

        # Assume database times are in local timezone and convert to UTC
        start_local = location_tz.localize(start_date.replace(tzinfo=None))
        end_local = location_tz.localize(end_date.replace(tzinfo=None))
        start_utc = start_local.astimezone(pytz.UTC)
        end_utc = end_local.astimezone(pytz.UTC)

        # Format dates for Meteomatics API (ISO format with Z suffix)
        start_iso = start_utc.strftime('%Y-%m-%dT%H:%M:%SZ')
        end_iso = end_utc.strftime('%Y-%m-%dT%H:%M:%SZ')
        
        # Build API URL
        # Parameters: temperature (°C), wind speed 80m (m/s), wind gusts 80m 1h (m/s), precipitation 1h (mm)
        parameters = "t_2m:C,wind_speed_80m:ms,wind_gusts_80m_1h:ms,precip_1h:mm"
        url = f"{self.base_url}/{start_iso}--{end_iso}:PT1H/{parameters}/{lat},{lon}/json"

        # Make API request
        data = self._make_request(url)

        # Normalize and process the response
        normalized_data = self._normalize_weather_data(data, lat, lon, workorder_id, start_date, end_date)

        # Cache the result
        cache.set(cache_key, normalized_data, self.cache_ttl)

        return normalized_data
    
    def _normalize_weather_data(self, data: Dict, lat: float, lon: float, workorder_id: int,
                               start_date: datetime, end_date: datetime) -> Dict:
        """
        Normalize API response to consistent format with dual timezone.

        Meteomatics API returns data with parameters grouped by type, each containing
        coordinates with date/value pairs. This method reorganizes the data by timestamp
        for easier consumption by the frontend.

        Returns:
            {
                'workorder_id': int,
                'location': {'lat': float, 'lon': float, 'timezone': str},
                'timeframe': {'start': str, 'end': str},
                'hourly': [
                    {
                        'time': str (ISO8601),
                        'temperature': float,
                        'windSpeed': float,
                        'windGusts': float,
                        'precipitation': float,
                        'localTime': str,
                        'rigaTime': str
                    },
                    ...
                ]
            }
        """
        try:
            location_tz = self._get_timezone_for_location(lat, lon)

            if 'data' not in data:
                raise WeatherHistoryServiceError("No data in API response")

            # Create a dictionary to organize data by timestamp
            time_data = {}

            # Process each parameter
            for parameter_data in data['data']:
                parameter = parameter_data.get('parameter', '')
                coordinates = parameter_data.get('coordinates', [])

                if not coordinates:
                    continue

                # Get the first coordinate (should only be one for point queries)
                coordinate_data = coordinates[0]
                dates = coordinate_data.get('dates', [])

                # Process each date/value pair
                for date_value_entry in dates:
                    timestamp_str = date_value_entry.get('date')
                    value = date_value_entry.get('value')

                    if not timestamp_str:
                        continue

                    # Parse timestamp (API returns UTC)
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    timestamp_key = timestamp.isoformat()

                    # Initialize time entry if not exists
                    if timestamp_key not in time_data:
                        # Convert UTC timestamp to both local and Riga time for display
                        # The API request was made with local times, so this conversion
                        # will show the correct local times that match the database
                        local_time = timestamp.astimezone(location_tz)
                        riga_time = timestamp.astimezone(self.RIGA_TIMEZONE)

                        time_data[timestamp_key] = {
                            'time': timestamp_key,
                            'temperature': None,
                            'windSpeed': None,
                            'windGusts': None,
                            'precipitation': None,
                            'localTime': local_time.strftime('%Y-%m-%d %H:%M'),
                            'rigaTime': riga_time.strftime('%Y-%m-%d %H:%M')
                        }

                    # Assign value based on parameter using mapping
                    field_name = self.PARAMETER_MAPPING.get(parameter)
                    if field_name:
                        time_data[timestamp_key][field_name] = value

            # Convert to sorted list
            hourly_data = sorted(time_data.values(), key=lambda x: x['time'])

            return {
                'workorder_id': workorder_id,
                'location': {
                    'lat': lat,
                    'lon': lon,
                    'timezone': str(location_tz)
                },
                'timeframe': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'hourly': hourly_data
            }

        except Exception as e:
            raise WeatherHistoryServiceError(f"Failed to process weather data: {str(e)}")
    
    def _get_timezone_for_location(self, lat: float, lon: float) -> pytz.timezone:
        """
        Get timezone for given coordinates using timezonefinder.

        Args:
            lat: Latitude coordinate
            lon: Longitude coordinate

        Returns:
            pytz.timezone object for the location
        """
        try:
            timezone_name = self._timezone_finder.timezone_at(lat=lat, lng=lon)

            if timezone_name:
                return pytz.timezone(timezone_name)
            else:
                return pytz.UTC

        except Exception:
            return pytz.UTC
